'use client';

import { useState } from 'react';
import { Boss, BossDropItem } from '@/types/boss';

interface AddBossModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAddBoss: (boss: Boss) => void;
}

const rarityOptions = ['common', 'rare', 'epic', 'legendary'] as const;
const typeOptions = ['weapon', 'armor', 'accessory', 'material', 'scroll', 'other'] as const;
const bossTypeOptions = ['field', 'dungeon', 'raid', 'epic'] as const;
const difficultyOptions = ['easy', 'medium', 'hard', 'extreme'] as const;

export default function AddBossModal({ isOpen, onClose, onAddBoss }: AddBossModalProps) {
  const [formData, setFormData] = useState({
    name: '',
    location: '',
    level: 1,
    respawnTime: 1,
    respawnVariance: 0,
    hp: 100000,
    type: 'field' as const,
    difficulty: 'medium' as const,
    minPlayers: 1,
    maxPlayers: 8,
    description: '',
    strategy: '',
    coordinates: { x: 0, y: 0, map: '' }
  });

  const [drops, setDrops] = useState<BossDropItem[]>([
    { name: '', type: 'material', rarity: 'common', dropRate: '' }
  ]);

  const handleInputChange = (field: string, value: string | number) => {
    if (field.startsWith('coordinates.')) {
      const coordField = field.split('.')[1];
      setFormData(prev => ({
        ...prev,
        coordinates: { ...prev.coordinates, [coordField]: value }
      }));
    } else {
      setFormData(prev => ({ ...prev, [field]: value }));
    }
  };

  const handleDropChange = (index: number, field: keyof BossDropItem, value: string) => {
    setDrops(prev => prev.map((drop, i) => 
      i === index ? { ...drop, [field]: value } : drop
    ));
  };

  const addDrop = () => {
    setDrops(prev => [...prev, { name: '', type: 'material', rarity: 'common', dropRate: '' }]);
  };

  const removeDrop = (index: number) => {
    setDrops(prev => prev.filter((_, i) => i !== index));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim() || !formData.location.trim()) {
      alert('Please fill in the boss name and location.');
      return;
    }

    const newBoss: Boss = {
      id: `custom-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      name: formData.name.trim(),
      location: formData.location.trim(),
      level: formData.level,
      respawnTime: formData.respawnTime,
      respawnVariance: formData.respawnVariance || undefined,
      hp: formData.hp,
      type: formData.type,
      difficulty: formData.difficulty,
      minPlayers: formData.minPlayers,
      maxPlayers: formData.maxPlayers,
      description: formData.description.trim() || undefined,
      strategy: formData.strategy.trim() || undefined,
      coordinates: formData.coordinates.map ? formData.coordinates : undefined,
      drops: drops.filter(drop => drop.name.trim()),
      isActive: false
    };

    onAddBoss(newBoss);
    handleReset();
    onClose();
  };

  const handleReset = () => {
    setFormData({
      name: '',
      location: '',
      level: 1,
      respawnTime: 1,
      respawnVariance: 0,
      hp: 100000,
      type: 'field',
      difficulty: 'medium',
      minPlayers: 1,
      maxPlayers: 8,
      description: '',
      strategy: '',
      coordinates: { x: 0, y: 0, map: '' }
    });
    setDrops([{ name: '', type: 'material', rarity: 'common', dropRate: '' }]);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50">
      {/* Background overlay */}
      <div
        className="absolute inset-0"
        onClick={onClose}
      />

      {/* Modal panel */}
      <div className="relative w-full max-w-4xl max-h-[90vh] overflow-y-auto bg-white dark:bg-gray-800 shadow-xl rounded-2xl p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
              Add New Boss
            </h3>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Boss Name *
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Location *
                </label>
                <input
                  type="text"
                  value={formData.location}
                  onChange={(e) => handleInputChange('location', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Level
                </label>
                <input
                  type="number"
                  min="1"
                  max="100"
                  value={formData.level}
                  onChange={(e) => handleInputChange('level', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Respawn Time (hours)
                </label>
                <input
                  type="number"
                  min="0.5"
                  step="0.5"
                  value={formData.respawnTime}
                  onChange={(e) => handleInputChange('respawnTime', parseFloat(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Respawn Variance (minutes)
                </label>
                <input
                  type="number"
                  min="0"
                  value={formData.respawnVariance}
                  onChange={(e) => handleInputChange('respawnVariance', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  HP
                </label>
                <input
                  type="number"
                  min="1000"
                  step="1000"
                  value={formData.hp}
                  onChange={(e) => handleInputChange('hp', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Type
                </label>
                <select
                  value={formData.type}
                  onChange={(e) => handleInputChange('type', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  {bossTypeOptions.map(type => (
                    <option key={type} value={type}>{type.charAt(0).toUpperCase() + type.slice(1)}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Difficulty
                </label>
                <select
                  value={formData.difficulty}
                  onChange={(e) => handleInputChange('difficulty', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  {difficultyOptions.map(difficulty => (
                    <option key={difficulty} value={difficulty}>{difficulty.charAt(0).toUpperCase() + difficulty.slice(1)}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Min Players
                </label>
                <input
                  type="number"
                  min="1"
                  max="20"
                  value={formData.minPlayers}
                  onChange={(e) => handleInputChange('minPlayers', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Max Players
                </label>
                <input
                  type="number"
                  min="1"
                  max="20"
                  value={formData.maxPlayers}
                  onChange={(e) => handleInputChange('maxPlayers', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>
            </div>

            {/* Coordinates */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  X Coordinate
                </label>
                <input
                  type="number"
                  value={formData.coordinates.x}
                  onChange={(e) => handleInputChange('coordinates.x', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Y Coordinate
                </label>
                <input
                  type="number"
                  value={formData.coordinates.y}
                  onChange={(e) => handleInputChange('coordinates.y', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Map Name
                </label>
                <input
                  type="text"
                  value={formData.coordinates.map}
                  onChange={(e) => handleInputChange('coordinates.map', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>
            </div>

            {/* Description and Strategy */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Description
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Strategy
                </label>
                <textarea
                  value={formData.strategy}
                  onChange={(e) => handleInputChange('strategy', e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>
            </div>

            {/* Drops Section */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Boss Drops
                </label>
                <button
                  type="button"
                  onClick={addDrop}
                  className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-md transition-colors"
                >
                  Add Drop
                </button>
              </div>

              <div className="space-y-3 max-h-60 overflow-y-auto">
                {drops.map((drop, index) => (
                  <div key={index} className="grid grid-cols-1 md:grid-cols-5 gap-2 p-3 border border-gray-200 dark:border-gray-600 rounded-md">
                    <div>
                      <input
                        type="text"
                        placeholder="Item name"
                        value={drop.name}
                        onChange={(e) => handleDropChange(index, 'name', e.target.value)}
                        className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      />
                    </div>

                    <div>
                      <select
                        value={drop.type}
                        onChange={(e) => handleDropChange(index, 'type', e.target.value)}
                        className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      >
                        {typeOptions.map(type => (
                          <option key={type} value={type}>{type.charAt(0).toUpperCase() + type.slice(1)}</option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <select
                        value={drop.rarity}
                        onChange={(e) => handleDropChange(index, 'rarity', e.target.value)}
                        className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      >
                        {rarityOptions.map(rarity => (
                          <option key={rarity} value={rarity}>{rarity.charAt(0).toUpperCase() + rarity.slice(1)}</option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <input
                        type="text"
                        placeholder="Drop rate"
                        value={drop.dropRate || ''}
                        onChange={(e) => handleDropChange(index, 'dropRate', e.target.value)}
                        className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      />
                    </div>

                    <div>
                      <button
                        type="button"
                        onClick={() => removeDrop(index)}
                        className="w-full px-2 py-1 bg-red-600 hover:bg-red-700 text-white text-sm rounded transition-colors"
                        disabled={drops.length === 1}
                      >
                        Remove
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Form Actions */}
            <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-600">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleReset}
                className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-md transition-colors"
              >
                Reset
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md transition-colors"
              >
                Add Boss
              </button>
            </div>
          </form>
      </div>
    </div>
  );
}
