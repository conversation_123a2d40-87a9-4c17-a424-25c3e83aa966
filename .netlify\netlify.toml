headersOrigin = "inline"
redirectsOrigin = "inline"

[functions]

[functions."*"]

[[plugins]]
origin = "default"
package = "@netlify/plugin-nextjs"

[plugins.inputs]

[build]
publish = "D:\\l2mbosstimer\\out"
publishOrigin = "config"
commandOrigin = "config"
command = "npm run build"

[build.environment]
NODE_VERSION = "18"
NPM_FLAGS = "--prefix=/opt/buildhome/repo"

[build.processing]

[build.processing.css]

[build.processing.html]

[build.processing.images]

[build.processing.js]

[build.services]

[[headers]]
for = "/*"

[headers.values]
X-Frame-Options = "DENY"
X-XSS-Protection = "1; mode=block"
X-Content-Type-Options = "nosniff"
Referrer-Policy = "strict-origin-when-cross-origin"

[[headers]]
for = "*.js"

[headers.values]
Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
for = "*.css"

[headers.values]
Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
for = "*.png"

[headers.values]
Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
for = "*.jpg"

[headers.values]
Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
for = "*.svg"

[headers.values]
Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
for = "/_next/static/*"

[headers.values]
Cache-Control = "public, max-age=31536000, immutable"

[[redirects]]
from = "/*"
to = "/index.html"
status = 200.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/_next/image/"
to = "/.netlify/images?url=:url&w=:width&q=:quality"
status = 200.0
force = false

[redirects.query]
url = ":url"
w = ":width"
q = ":quality"

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/_ipx/*"
to = "/.netlify/images?url=:url&w=:width&q=:quality"
status = 200.0
force = false

[redirects.query]
url = ":url"
w = ":width"
q = ":quality"

[redirects.conditions]

[redirects.headers]